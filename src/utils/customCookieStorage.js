
class CustomCookieStorage {
  set<PERSON><PERSON>ie(key, value, options = {}) {
    set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(key, value, options);
  }

  get<PERSON><PERSON><PERSON>(key) {
    return getCook<PERSON><PERSON><PERSON><PERSON>(key);
  }

  removeCookie(key, options = {}) {
    removeCookie<PERSON><PERSON>per(key, options);
  }

  getUtmCookie(utmKey) {
    return getUtmCookie(utmKey);
  }

  setUtmCookie(utmKey, value, options = {}) {
    setUtmCookie(utmKey, value, options);
  }

  removeUtmCookie(utmKey, options = {}) {
    removeUtmCookie(utmKey, options);
  }

  collectUtmCookies() {
    return collectUtmCookies();
  }

  clearAllUtmCookies() {
    const utmParams = this.collectUtmCookies();
    Object.keys(utmParams).forEach((key) => {
      this.removeUtmCookie(key);
    });

    // Also clear session storage
    try {
      sessionStorage.removeItem(UTM_SESSION_KEY);
    } catch (error) {
      console.warn("Failed to clear UTM session storage:", error);
    }
  }
}

export const customCookieStorage = new CustomCookieStorage();

export function getRootDomain() {
  const hostname = window.location.hostname;

  // For localhost or IP addresses, don't set domain
  if (hostname === "localhost" || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    return null;
  }

  // Split hostname and get the last two parts (domain.tld)
  const parts = hostname.split(".");
  if (parts.length >= 2) {
    return `.${parts.slice(-2).join(".")}`;
  }

  return null;
}

function getCookieHelper(name) {
  if (typeof window === "undefined") return null;

  const cookies = document.cookie.split("; ");
  const cookie = cookies.find((c) => c.startsWith(`${name}=`));

  if (!cookie) return null;

  const cookieValue = cookie.split("=")[1];

  try {
    return JSON.parse(decodeURIComponent(cookieValue));
  } catch {
    return decodeURIComponent(cookieValue);
  }
}

function setCookieHelper(name, value, options = {}) {
  const {
    days = 7,
    path = "/",
    domain = getRootDomain(),
    secure = window.location.protocol === "https:",
    sameSite = "Lax",
  } = options;

  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);

  let cookieValue;

  if (typeof value === "string") {
    cookieValue = encodeURIComponent(value);
  } else {
    cookieValue = encodeURIComponent(JSON.stringify(value));
  }

  document.cookie = `${name}=${cookieValue}; expires=${expires.toUTCString()}; path=${path}${
    domain ? `; Domain=${domain}` : ""
  }${secure ? "; Secure" : ""}; SameSite=${sameSite}`;
}

function removeCookieHelper(name, options = {}) {
  const { path = "/", domain = getRootDomain() } = options;
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}${
    domain ? `; Domain=${domain}` : ""
  }`;
}

export function setUtmCookie(utmKey, value, options = {}) {
  if (!utmKey.startsWith("utm_")) {
    throw new Error('UTM key must start with "utm_"');
  }
  
  try {
    setCookieHelper(utmKey, value, options);
  } catch (error) {
    console.warn(`Failed to set UTM cookie ${utmKey}:`, error);
  } finally {
    // Always store in session storage as fallback
    setUtmSessionStorage(utmKey, value);
  }
}

export function getUtmCookie(utmKey) {
  if (!utmKey.startsWith("utm_")) {
    throw new Error('UTM key must start with "utm_"');
  }

  // Try to get from cookies first
  try {
    const cookieValue = getCookieHelper(utmKey);
    if (cookieValue !== null) {
      return cookieValue;
    }
  } catch (error) {
    console.warn(`Failed to get UTM cookie ${utmKey}:`, error);
  }

  // Fallback to session storage
  return getUtmSessionStorage(utmKey);
}

export function removeUtmCookie(utmKey, options = {}) {
  if (!utmKey.startsWith("utm_")) {
    throw new Error('UTM key must start with "utm_"');
  }

  try {
    removeCookieHelper(utmKey, options);
  } catch (error) {
    console.warn(`Failed to remove UTM cookie ${utmKey}:`, error);
  } finally {
    // Always remove from session storage as well
    removeUtmSessionStorage(utmKey);
  }
}

export function collectUtmCookies() {
  if (typeof window === "undefined") return {};

  let utmParams = {};

  // Try to get from cookies first
  try {
    const cookies = document.cookie.split("; ");
    cookies.forEach((cookie) => {
      const [name, value] = cookie.split("=");
      if (name && name.startsWith("utm_") && value) {
        try {
          utmParams[name] = String(JSON.parse(decodeURIComponent(value)));
        } catch {
          utmParams[name] = decodeURIComponent(value);
        }
      }
    });
  } catch (error) {
    console.warn("Failed to collect UTM cookies:", error);
  }

  // Merge with session storage values (session storage takes precedence for missing cookie values)
  const sessionUtmParams = getUtmSessionStorageAll();
  Object.keys(sessionUtmParams).forEach((key) => {
    if (!utmParams[key]) {
      utmParams[key] = sessionUtmParams[key];
    }
  });

  return utmParams;
}

// Session storage helper functions for UTM parameters
const UTM_SESSION_KEY = "utm_params";

function setUtmSessionStorage(utmKey, value) {
  if (typeof window === "undefined") return;

  try {
    const existingUtmParams = getUtmSessionStorageAll();
    existingUtmParams[utmKey] = value;
    sessionStorage.setItem(UTM_SESSION_KEY, JSON.stringify(existingUtmParams));
  } catch (error) {
    console.warn(`Failed to set UTM session storage ${utmKey}:`, error);
  }
}

function getUtmSessionStorage(utmKey) {
  if (typeof window === "undefined") return null;

  try {
    const utmParams = getUtmSessionStorageAll();
    return utmParams[utmKey] || null;
  } catch (error) {
    console.warn(`Failed to get UTM session storage ${utmKey}:`, error);
    return null;
  }
}

function getUtmSessionStorageAll() {
  if (typeof window === "undefined") return {};

  try {
    const stored = sessionStorage.getItem(UTM_SESSION_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.warn("Failed to get all UTM session storage:", error);
    return {};
  }
}

function removeUtmSessionStorage(utmKey) {
  if (typeof window === "undefined") return;

  try {
    const existingUtmParams = getUtmSessionStorageAll();
    delete existingUtmParams[utmKey];

    if (Object.keys(existingUtmParams).length === 0) {
      sessionStorage.removeItem(UTM_SESSION_KEY);
    } else {
      sessionStorage.setItem(UTM_SESSION_KEY, JSON.stringify(existingUtmParams));
    }
  } catch (error) {
    console.warn(`Failed to remove UTM session storage ${utmKey}:`, error);
  }
}
