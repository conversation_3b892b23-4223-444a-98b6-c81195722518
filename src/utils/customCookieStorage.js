class CustomCookieStorage {
  set<PERSON><PERSON>ie(key, value, options = {}) {
    set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(key, value, options);
  }

  get<PERSON><PERSON><PERSON>(key) {
    return getCookie<PERSON><PERSON><PERSON>(key);
  }

  removeCookie(key, options = {}) {
    removeCookie<PERSON><PERSON>per(key, options);
  }

  getUtmCookie(utmKey) {
    return getUtmCookie(utmKey);
  }

  setUtmCookie(utmKey, value, options = {}) {
    setUtmCookie(utmKey, value, options);
  }

  removeUtmCookie(utmKey, options = {}) {
    removeUtmCookie(utmKey, options);
  }

  collectUtmCookies() {
    return collectUtmCookies();
  }

  clearAllUtmCookies() {
    const utmParams = this.collectUtmCookies();
    Object.keys(utmParams).forEach((key) => {
      this.removeUtmCookie(key);
    });
  }
}

export const customCookieStorage = new CustomCookieStorage();

export function getRootDomain() {
  const hostname = window.location.hostname;

  // For localhost or IP addresses, don't set domain
  if (hostname === "localhost" || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    return null;
  }

  // Split hostname and get the last two parts (domain.tld)
  const parts = hostname.split(".");
  if (parts.length >= 2) {
    return `.${parts.slice(-2).join(".")}`;
  }

  return null;
}

function getCookieHelper(name) {
  if (typeof window === "undefined") return null;

  const cookies = document.cookie.split("; ");
  const cookie = cookies.find((c) => c.startsWith(`${name}=`));

  if (!cookie) return null;

  const cookieValue = cookie.split("=")[1];

  try {
    return JSON.parse(decodeURIComponent(cookieValue));
  } catch {
    return decodeURIComponent(cookieValue);
  }
}

function setCookieHelper(name, value, options = {}) {
  const {
    days = 7,
    path = "/",
    domain = getRootDomain(),
    secure = window.location.protocol === "https:",
    sameSite = "Lax",
  } = options;

  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);

  let cookieValue;

  if (typeof value === "string") {
    cookieValue = encodeURIComponent(value);
  } else {
    cookieValue = encodeURIComponent(JSON.stringify(value));
  }

  document.cookie = `${name}=${cookieValue}; expires=${expires.toUTCString()}; path=${path}${
    domain ? `; Domain=${domain}` : ""
  }${secure ? "; Secure" : ""}; SameSite=${sameSite}`;
}

function removeCookieHelper(name, options = {}) {
  const { path = "/", domain = getRootDomain() } = options;
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}${
    domain ? `; Domain=${domain}` : ""
  }`;
}

export function setUtmCookie(utmKey, value, options = {}) {
  if (!utmKey.startsWith("utm_")) {
    throw new Error('UTM key must start with "utm_"');
  }
  setCookieHelper(utmKey, value, options);
}

export function getUtmCookie(utmKey) {
  if (!utmKey.startsWith("utm_")) {
    throw new Error('UTM key must start with "utm_"');
  }
  return getCookieHelper(utmKey);
}

export function removeUtmCookie(utmKey, options = {}) {
  if (!utmKey.startsWith("utm_")) {
    throw new Error('UTM key must start with "utm_"');
  }
  removeCookieHelper(utmKey, options);
}

export function collectUtmCookies() {
  if (typeof window === "undefined") return {};

  const utmParams = {};
  const cookies = document.cookie.split("; ");

  cookies.forEach((cookie) => {
    const [name, value] = cookie.split("=");
    if (name && name.startsWith("utm_") && value) {
      try {
        utmParams[name] = String(JSON.parse(decodeURIComponent(value)));
      } catch {
        utmParams[name] = decodeURIComponent(value);
      }
    }
  });

  return utmParams;
}
